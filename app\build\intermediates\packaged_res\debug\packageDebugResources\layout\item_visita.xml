<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/txtNombre"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Nombre Apellido"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/txtRut"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="RUT: 12345678-9"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/txtDepartamento"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Depto: 101"
            android:textSize="14sp"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/txtFechaIngreso"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Ingreso: 01/01/2024 10:30"
            android:textSize="14sp"
            android:textColor="@android:color/holo_green_dark"
            android:layout_marginBottom="4dp" />

        <TextView
            android:id="@+id/txtFechaSalida"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Salida: 01/01/2024 18:30"
            android:textSize="14sp"
            android:textColor="@android:color/holo_red_dark"
            android:layout_marginBottom="8dp"
            android:visibility="gone" />

        <Button
            android:id="@+id/btnRegistrarSalida"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="REGISTRAR SALIDA"
            style="@style/Widget.Material3.Button.OutlinedButton" />

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>