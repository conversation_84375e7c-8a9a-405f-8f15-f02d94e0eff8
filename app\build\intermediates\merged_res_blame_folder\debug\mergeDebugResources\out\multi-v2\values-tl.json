{"logs": [{"outputFile": "com.tarea.angel_riquelme_tarea6.app-mergeDebugResources-32:/values-tl/values-tl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4d439e7aeec2141ba6a69ea06bb18f1b\\transformed\\appcompat-1.7.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,324,437,525,631,746,826,903,994,1087,1182,1276,1376,1469,1564,1658,1749,1840,1924,2033,2143,2244,2354,2472,2580,2743,2845", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "211,319,432,520,626,741,821,898,989,1082,1177,1271,1371,1464,1559,1653,1744,1835,1919,2028,2138,2239,2349,2467,2575,2738,2840,2925"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "330,441,549,662,750,856,971,1051,1128,1219,1312,1407,1501,1601,1694,1789,1883,1974,2065,2149,2258,2368,2469,2579,2697,2805,2968,9791", "endColumns": "110,107,112,87,105,114,79,76,90,92,94,93,99,92,94,93,90,90,83,108,109,100,109,117,107,162,101,84", "endOffsets": "436,544,657,745,851,966,1046,1123,1214,1307,1402,1496,1596,1689,1784,1878,1969,2060,2144,2253,2363,2464,2574,2692,2800,2963,3065,9871"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\a8b84297fb3b26eb306fb19c57dc868d\\transformed\\core-1.16.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,355,452,559,667,789", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "147,249,350,447,554,662,784,885"}, "to": {"startLines": "38,39,40,41,42,43,44,116", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3507,3604,3706,3807,3904,4011,4119,10116", "endColumns": "96,101,100,96,106,107,121,100", "endOffsets": "3599,3701,3802,3899,4006,4114,4236,10212"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4af67bdd009fb7c7a1b151f634691926\\transformed\\material-1.12.0\\res\\values-tl\\values-tl.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,280,364,444,530,627,717,822,958,1043,1103,1168,1267,1335,1394,1483,1551,1618,1681,1756,1824,1878,1998,2056,2118,2172,2247,2389,2479,2557,2651,2734,2819,2964,3048,3131,3277,3373,3450,3508,3559,3625,3699,3777,3848,3934,4008,4087,4160,4232,4348,4452,4525,4624,4724,4798,4873,4980,5032,5121,5188,5279,5373,5435,5499,5562,5632,5751,5856,5965,6065,6127,6182,6267,6354,6432", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "275,359,439,525,622,712,817,953,1038,1098,1163,1262,1330,1389,1478,1546,1613,1676,1751,1819,1873,1993,2051,2113,2167,2242,2384,2474,2552,2646,2729,2814,2959,3043,3126,3272,3368,3445,3503,3554,3620,3694,3772,3843,3929,4003,4082,4155,4227,4343,4447,4520,4619,4719,4793,4868,4975,5027,5116,5183,5274,5368,5430,5494,5557,5627,5746,5851,5960,6060,6122,6177,6262,6349,6427,6502"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3154,3234,3320,3417,4241,4346,4482,4567,4627,4692,4791,4859,4918,5007,5075,5142,5205,5280,5348,5402,5522,5580,5642,5696,5771,5913,6003,6081,6175,6258,6343,6488,6572,6655,6801,6897,6974,7032,7083,7149,7223,7301,7372,7458,7532,7611,7684,7756,7872,7976,8049,8148,8248,8322,8397,8504,8556,8645,8712,8803,8897,8959,9023,9086,9156,9275,9380,9489,9589,9651,9706,9876,9963,10041", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,113,114,115", "endColumns": "12,83,79,85,96,89,104,135,84,59,64,98,67,58,88,67,66,62,74,67,53,119,57,61,53,74,141,89,77,93,82,84,144,83,82,145,95,76,57,50,65,73,77,70,85,73,78,72,71,115,103,72,98,99,73,74,106,51,88,66,90,93,61,63,62,69,118,104,108,99,61,54,84,86,77,74", "endOffsets": "325,3149,3229,3315,3412,3502,4341,4477,4562,4622,4687,4786,4854,4913,5002,5070,5137,5200,5275,5343,5397,5517,5575,5637,5691,5766,5908,5998,6076,6170,6253,6338,6483,6567,6650,6796,6892,6969,7027,7078,7144,7218,7296,7367,7453,7527,7606,7679,7751,7867,7971,8044,8143,8243,8317,8392,8499,8551,8640,8707,8798,8892,8954,9018,9081,9151,9270,9375,9484,9584,9646,9701,9786,9958,10036,10111"}}]}]}