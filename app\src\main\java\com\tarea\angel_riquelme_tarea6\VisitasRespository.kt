package com.tarea.angel_riquelme_tarea6

import io.realm.Realm
import io.realm.RealmResults
import java.util.*

class VisitasRepository {

    private val realm: Realm by lazy { Realm.getDefaultInstance() }

    fun insertarVisita(
        rut: String,
        nombre: String,
        apellido: String,
        departamento: String
    ): Bo<PERSON>an {
        return try {
            realm.executeTransaction { realm ->
                val visita = VisitaModel(
                    rut = rut,
                    nombre = nombre,
                    apellido = apellido,
                    departamento = departamento
                )
                realm.copyToRealm(visita)
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    fun obtenerTodasLasVisitas(): List<VisitaModel> {
        return realm.where(VisitaModel::class.java)
            .findAll()
            .let { realm.copyFromRealm(it) }
    }

    fun actualizarFechaSalida(visitaId: String, fechaSalida: Date): Boolean {
        return try {
            realm.executeTransaction { realm ->
                val visita = realm.where(VisitaModel::class.java)
                    .equalTo("id", visitaId)
                    .findFirst()

                visita?.fechaSalida = fechaSalida
            }
            true
        } catch (e: Exception) {
            e.printStackTrace()
            false
        }
    }

    fun obtenerVisitaPorId(visitaId: String): VisitaModel? {
        return realm.where(VisitaModel::class.java)
            .equalTo("id", visitaId)
            .findFirst()
            ?.let { realm.copyFromRealm(it) }
    }

    fun close() {
        if (!realm.isClosed) {
            realm.close()
        }
    }
}