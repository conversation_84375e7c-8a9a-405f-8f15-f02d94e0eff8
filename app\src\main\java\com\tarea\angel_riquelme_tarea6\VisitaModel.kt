package com.tarea.angel_riquelme_tarea6

import io.realm.RealmObject
import io.realm.annotations.PrimaryKey
import io.realm.annotations.Required
import java.util.*

open class VisitaModel : RealmObject() {

    @PrimaryKey
    var id: String = UUID.randomUUID().toString()

    @Required
    var rut: String = ""

    @Required
    var nombre: String = ""

    @Required
    var apellido: String = ""

    @Required
    var fechaIngreso: Date = Date()

    @Required
    var departamento: String = ""

    var fechaSalida: Date? = null

    // Constructor vacío requerido por Realm
    constructor()

    // Constructor con parámetros
    constructor(
        rut: String,
        nombre: String,
        apellido: String,
        departamento: String,
        fechaIngreso: Date = Date()
    ) {
        this.rut = rut
        this.nombre = nombre
        this.apellido = apellido
        this.departamento = departamento
        this.fechaIngreso = fechaIngreso
    }
}