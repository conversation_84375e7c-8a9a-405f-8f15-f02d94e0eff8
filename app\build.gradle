plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
    id 'io.realm.kotlin'
}

android {
    namespace 'com.tarea.angel_riquelme_tarea6'
    compileSdk 35

    defaultConfig {
        applicationId "com.tarea.angel_riquelme_tarea6"
        minSdk 28
        targetSdk 35
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    kotlinOptions {
        jvmTarget = '11'
    }
}

dependencies {
    implementation libs.androidx.core.ktx
    implementation libs.androidx.appcompat
    implementation libs.material
    implementation libs.androidx.activity
    implementation libs.androidx.constraintlayout
    testImplementation libs.junit
    androidTestImplementation libs.androidx.junit
    androidTestImplementation libs.androidx.espresso.core

    // RecyclerView
    implementation 'androidx.recyclerview:recyclerview:1.3.2'

    // Realm Database - Modern Kotlin version
    implementation 'io.realm.kotlin:library-base:1.11.0'
}