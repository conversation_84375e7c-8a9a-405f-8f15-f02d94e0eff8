-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:2:1-30:12
INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:2:1-30:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4af67bdd009fb7c7a1b151f634691926\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b060f83915b5b1a2b332fdc1259be16\transformed\constraintlayout-2.2.1\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2dc7bad6e82e37a4efd4f43bc58e5aa\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d439e7aeec2141ba6a69ea06bb18f1b\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cfc0ab9507deafc635c83f62fa8912b\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e3b50a19e22b093e2b3e2cd25df45c\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86b75d3ea28cde7d7aaa1a5b1c13e23e\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10bb9d8d9cf65b4a2e292bc1064fc79f\transformed\activity-1.10.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb27fdc112bd448f205f48f49eb4160c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b2505c096150b4bf97736c4adaf004\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7abd4b471bd7739ffe76595e798a16ae\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\076cbe029f0453a6cd26b7a9b376047a\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdc1f107c7775b563b5ab504332f3b7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29ebba5233866400ae84fd5588e7ecb7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa281884c9dae376e387c59e8ea20df3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7650acaa569191e33a24267bd5077b06\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e30fafb99c43dd26b19e2edca7492c\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d81a2e9af4a928959350d5726a6df\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6e590d9f4529a43a0d0d272ec3cf3de\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\274ab74444c3b589256007bf446708cd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f047349652202c8a1e76e39e1cfa9f5\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51bce1a5da020783feccc9ce1ecefd02\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\549cdf24f98905b48055096957adaadd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9eeb20a5c229dbde9e5abeab0c51437b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d95298576f4b13c4c157ed04aa783f\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26ebe6b5ffe5a1a7d1efcebdf72fe62e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30aaf2e8a46bdcb6d04055fb868c200b\transformed\core-ktx-1.16.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94822bbb7f5bb7a8b2c68eeb0641de1\transformed\core-viewtree-1.0.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bab44f78bfb3ecd454df86ad6b9a41b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a33c6b844bfa6e927673815297529ec\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:17:1-55:12
MERGED from [io.realm.kotlin:library-base-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c3493a781530719c4713eb02300a28\transformed\library-base-release\AndroidManifest.xml:18:1-26:12
MERGED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:18:1-38:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f96342070988a2a6deea64c88bf71a86\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b8437dffcf80d9e62c84b3ec67c7d2d\transformed\tracing-1.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e34befa94eae3f7c163bf2405514f2f8\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4071842010688e29402a14056c4b500\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51de3caf103a21a2ffd48ca020d2ec66\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ce08abc3b7e663ef861ec86ee99f4c\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdccb00578b2796b2e4a895100872a01\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\720b507e9104f0d3d23d33721858eeeb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bf6b7882a9c9ba8f6bacda6d039a27d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [org.mongodb.kbson:kbson-android-debug:0.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18fa111272b6bfcc4f5e373275b314d7\transformed\kbson-debug\AndroidManifest.xml:18:1-27:12
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92091a107df0f5da1f2c2d7eda9e1774\transformed\relinker-1.4.5\AndroidManifest.xml:2:1-7:12
	package
		INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:2:11-69
application
ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:4:1-28:15
INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:4:1-28:15
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4af67bdd009fb7c7a1b151f634691926\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4af67bdd009fb7c7a1b151f634691926\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b060f83915b5b1a2b332fdc1259be16\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b060f83915b5b1a2b332fdc1259be16\transformed\constraintlayout-2.2.1\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:23:5-53:19
MERGED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:26:5-36:19
MERGED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:26:5-36:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f96342070988a2a6deea64c88bf71a86\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f96342070988a2a6deea64c88bf71a86\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4071842010688e29402a14056c4b500\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4071842010688e29402a14056c4b500\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
	android:label
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:8:5-37
	android:icon
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:7:5-39
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:6:5-31
	android:theme
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:9:5-52
	android:name
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:5:5-39
activity#com.tarea.angel_riquelme_tarea6.MainActivity
ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:11:5-18:16
	android:exported
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:13:9-32
	android:name
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:12:9-37
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:14:9-17:25
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:15:13-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:15:21-62
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:16:13-73
	android:name
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:16:23-70
provider#com.tarea.angel_riquelme_tarea6.VisitasContentProvider
ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:21:5-26:79
	android:readPermission
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:25:9-74
	android:authorities
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:23:9-57
	android:exported
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:24:9-32
	android:writePermission
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:26:9-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:22:9-47
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4af67bdd009fb7c7a1b151f634691926\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4af67bdd009fb7c7a1b151f634691926\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b060f83915b5b1a2b332fdc1259be16\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b060f83915b5b1a2b332fdc1259be16\transformed\constraintlayout-2.2.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2dc7bad6e82e37a4efd4f43bc58e5aa\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a2dc7bad6e82e37a4efd4f43bc58e5aa\transformed\appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d439e7aeec2141ba6a69ea06bb18f1b\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4d439e7aeec2141ba6a69ea06bb18f1b\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cfc0ab9507deafc635c83f62fa8912b\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1cfc0ab9507deafc635c83f62fa8912b\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e3b50a19e22b093e2b3e2cd25df45c\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d6e3b50a19e22b093e2b3e2cd25df45c\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86b75d3ea28cde7d7aaa1a5b1c13e23e\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\86b75d3ea28cde7d7aaa1a5b1c13e23e\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10bb9d8d9cf65b4a2e292bc1064fc79f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.10.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10bb9d8d9cf65b4a2e292bc1064fc79f\transformed\activity-1.10.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb27fdc112bd448f205f48f49eb4160c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\eb27fdc112bd448f205f48f49eb4160c\transformed\emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b2505c096150b4bf97736c4adaf004\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46b2505c096150b4bf97736c4adaf004\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7abd4b471bd7739ffe76595e798a16ae\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7abd4b471bd7739ffe76595e798a16ae\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\076cbe029f0453a6cd26b7a9b376047a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\076cbe029f0453a6cd26b7a9b376047a\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdc1f107c7775b563b5ab504332f3b7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9fdc1f107c7775b563b5ab504332f3b7\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29ebba5233866400ae84fd5588e7ecb7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29ebba5233866400ae84fd5588e7ecb7\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa281884c9dae376e387c59e8ea20df3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fa281884c9dae376e387c59e8ea20df3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7650acaa569191e33a24267bd5077b06\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7650acaa569191e33a24267bd5077b06\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e30fafb99c43dd26b19e2edca7492c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53e30fafb99c43dd26b19e2edca7492c\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d81a2e9af4a928959350d5726a6df\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\773d81a2e9af4a928959350d5726a6df\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6e590d9f4529a43a0d0d272ec3cf3de\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c6e590d9f4529a43a0d0d272ec3cf3de\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\274ab74444c3b589256007bf446708cd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\274ab74444c3b589256007bf446708cd\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f047349652202c8a1e76e39e1cfa9f5\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8f047349652202c8a1e76e39e1cfa9f5\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51bce1a5da020783feccc9ce1ecefd02\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51bce1a5da020783feccc9ce1ecefd02\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\549cdf24f98905b48055096957adaadd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\549cdf24f98905b48055096957adaadd\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9eeb20a5c229dbde9e5abeab0c51437b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9eeb20a5c229dbde9e5abeab0c51437b\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d95298576f4b13c4c157ed04aa783f\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8d95298576f4b13c4c157ed04aa783f\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26ebe6b5ffe5a1a7d1efcebdf72fe62e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\26ebe6b5ffe5a1a7d1efcebdf72fe62e\transformed\lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30aaf2e8a46bdcb6d04055fb868c200b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\30aaf2e8a46bdcb6d04055fb868c200b\transformed\core-ktx-1.16.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94822bbb7f5bb7a8b2c68eeb0641de1\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-viewtree:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b94822bbb7f5bb7a8b2c68eeb0641de1\transformed\core-viewtree-1.0.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bab44f78bfb3ecd454df86ad6b9a41b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1bab44f78bfb3ecd454df86ad6b9a41b\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a33c6b844bfa6e927673815297529ec\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0a33c6b844bfa6e927673815297529ec\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [io.realm.kotlin:library-base-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c3493a781530719c4713eb02300a28\transformed\library-base-release\AndroidManifest.xml:22:5-24:41
MERGED from [io.realm.kotlin:library-base-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a3c3493a781530719c4713eb02300a28\transformed\library-base-release\AndroidManifest.xml:22:5-24:41
MERGED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:22:5-24:41
MERGED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:22:5-24:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f96342070988a2a6deea64c88bf71a86\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f96342070988a2a6deea64c88bf71a86\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b8437dffcf80d9e62c84b3ec67c7d2d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.tracing:tracing:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b8437dffcf80d9e62c84b3ec67c7d2d\transformed\tracing-1.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e34befa94eae3f7c163bf2405514f2f8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e34befa94eae3f7c163bf2405514f2f8\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4071842010688e29402a14056c4b500\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4071842010688e29402a14056c4b500\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51de3caf103a21a2ffd48ca020d2ec66\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\51de3caf103a21a2ffd48ca020d2ec66\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ce08abc3b7e663ef861ec86ee99f4c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\10ce08abc3b7e663ef861ec86ee99f4c\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdccb00578b2796b2e4a895100872a01\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fdccb00578b2796b2e4a895100872a01\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\720b507e9104f0d3d23d33721858eeeb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\720b507e9104f0d3d23d33721858eeeb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bf6b7882a9c9ba8f6bacda6d039a27d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\8bf6b7882a9c9ba8f6bacda6d039a27d\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [org.mongodb.kbson:kbson-android-debug:0.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18fa111272b6bfcc4f5e373275b314d7\transformed\kbson-debug\AndroidManifest.xml:23:5-25:41
MERGED from [org.mongodb.kbson:kbson-android-debug:0.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\18fa111272b6bfcc4f5e373275b314d7\transformed\kbson-debug\AndroidManifest.xml:23:5-25:41
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92091a107df0f5da1f2c2d7eda9e1774\transformed\relinker-1.4.5\AndroidManifest.xml:5:5-43
MERGED from [com.getkeepsafe.relinker:relinker:1.4.5] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92091a107df0f5da1f2c2d7eda9e1774\transformed\relinker-1.4.5\AndroidManifest.xml:5:5-43
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:27:9-35:20
MERGED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:27:9-35:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f96342070988a2a6deea64c88bf71a86\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f96342070988a2a6deea64c88bf71a86\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
permission#com.tarea.angel_riquelme_tarea6.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
uses-permission#com.tarea.angel_riquelme_tarea6.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
meta-data#io.realm.kotlin.internal.RealmInitializer
ADDED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:32:13-34:52
	android:value
		ADDED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:34:17-49
	android:name
		ADDED from [io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:33:17-73
