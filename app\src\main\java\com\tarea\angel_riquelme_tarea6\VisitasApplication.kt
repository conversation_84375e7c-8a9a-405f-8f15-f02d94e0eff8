package com.tarea.angel_riquelme_tarea6

import android.app.Application
import io.realm.Realm
import io.realm.RealmConfiguration

class VisitasApplication : Application() {

    override fun onCreate() {
        super.onCreate()

        // Inicializar Realm
        Realm.init(this)

        // Configurar Realm
        val config = RealmConfiguration.Builder()
            .name("visitas.realm")
            .schemaVersion(1)
            .deleteRealmIfMigrationNeeded() // Solo para desarrollo
            .build()

        Realm.setDefaultConfiguration(config)
    }
}