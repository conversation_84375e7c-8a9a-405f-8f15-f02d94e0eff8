1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.tarea.angel_riquelme_tarea6"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <permission
11-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:22:5-24:47
12        android:name="com.tarea.angel_riquelme_tarea6.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
12-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:23:9-81
13        android:protectionLevel="signature" />
13-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:24:9-44
14
15    <uses-permission android:name="com.tarea.angel_riquelme_tarea6.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:26:5-97
15-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:26:22-94
16
17    <application
17-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:4:1-28:15
18        android:name="com.tarea.angel_riquelme_tarea6.VisitasApplication"
18-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:5:5-39
19        android:allowBackup="true"
19-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:6:5-31
20        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
20-->[androidx.core:core:1.16.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a8b84297fb3b26eb306fb19c57dc868d\transformed\core-1.16.0\AndroidManifest.xml:28:18-86
21        android:debuggable="true"
22        android:extractNativeLibs="false"
23        android:icon="@mipmap/ic_launcher"
23-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:7:5-39
24        android:label="@string/app_name"
24-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:8:5-37
25        android:testOnly="true"
26        android:theme="@style/Theme.Material3.DayNight" >
26-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:9:5-52
27        <activity
27-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:11:5-18:16
28            android:name="com.tarea.angel_riquelme_tarea6.MainActivity"
28-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:12:9-37
29            android:exported="true" >
29-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:13:9-32
30            <intent-filter>
30-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:14:9-17:25
31                <action android:name="android.intent.action.MAIN" />
31-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:15:13-65
31-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:15:21-62
32
33                <category android:name="android.intent.category.LAUNCHER" />
33-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:16:13-73
33-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:16:23-70
34            </intent-filter>
35        </activity>
36
37        <!-- Content Provider para compartir datos -->
38        <provider
38-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:21:5-26:79
39            android:name="com.tarea.angel_riquelme_tarea6.VisitasContentProvider"
39-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:22:9-47
40            android:authorities="com.tuapp.visitas.provider"
40-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:23:9-57
41            android:exported="true"
41-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:24:9-32
42            android:readPermission="android.permission.READ_EXTERNAL_STORAGE"
42-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:25:9-74
43            android:writePermission="android.permission.WRITE_EXTERNAL_STORAGE" />
43-->C:\Users\<USER>\Desktop\ARCHIVOS\Tareas Instituto\PROGRAMACION MOVIL\Angel_Riquelme_Tarea6\app\src\main\AndroidManifest.xml:26:9-76
44        <provider
44-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
45            android:name="androidx.startup.InitializationProvider"
45-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
46            android:authorities="com.tarea.angel_riquelme_tarea6.androidx-startup"
46-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
47            android:exported="false" >
47-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
48            <meta-data
48-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
49                android:name="androidx.emoji2.text.EmojiCompatInitializer"
49-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
50                android:value="androidx.startup" />
50-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3571e17c919d6be4162a332281bb1ceb\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
51            <meta-data
51-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
52                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
52-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
53                android:value="androidx.startup" />
53-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\52f5188704019fc6def9bc7af5404c9e\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
54            <meta-data
54-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:29:13-31:52
55                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
55-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:30:17-85
56                android:value="androidx.startup" />
56-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:31:17-49
57            <meta-data
57-->[io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:32:13-34:52
58                android:name="io.realm.kotlin.internal.RealmInitializer"
58-->[io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:33:17-73
59                android:value="androidx.startup" />
59-->[io.realm.kotlin:cinterop-android:1.11.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\71491ab7a1580d5898c0a9981e0ef6fa\transformed\cinterop-release\AndroidManifest.xml:34:17-49
60        </provider>
61
62        <receiver
62-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:34:9-52:20
63            android:name="androidx.profileinstaller.ProfileInstallReceiver"
63-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:35:13-76
64            android:directBootAware="false"
64-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:36:13-44
65            android:enabled="true"
65-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:37:13-35
66            android:exported="true"
66-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:38:13-36
67            android:permission="android.permission.DUMP" >
67-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:39:13-57
68            <intent-filter>
68-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:40:13-42:29
69                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:17-91
69-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:41:25-88
70            </intent-filter>
71            <intent-filter>
71-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:43:13-45:29
72                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:17-85
72-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:44:25-82
73            </intent-filter>
74            <intent-filter>
74-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:46:13-48:29
75                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:17-88
75-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:47:25-85
76            </intent-filter>
77            <intent-filter>
77-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:49:13-51:29
78                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:17-95
78-->[androidx.profileinstaller:profileinstaller:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c04b68384a9012637f90d9b3999ec40f\transformed\profileinstaller-1.4.0\AndroidManifest.xml:50:25-92
79            </intent-filter>
80        </receiver>
81    </application>
82
83</manifest>
